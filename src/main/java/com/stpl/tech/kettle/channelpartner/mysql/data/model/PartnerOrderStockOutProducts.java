package com.stpl.tech.kettle.channelpartner.mysql.data.model;


import lombok.Data;
import org.joda.time.DateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "PARTNER_ORDER_STOCK_OUT_PRODUCTS")
public class PartnerOrderStockOutProducts {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "ORDER_ID", nullable = false,length = 50)
    private Integer orderId;

    @Column(name = "PRODUCT_ID", nullable = false, length = 500)
    private String productId;

    @Column(name = "PRODUCT_NAME" , nullable = false, length = 500)
    private String productName;

    @Column(name = "DIMENSION" , nullable = false, length = 500)
    private String dimension;

    @Column(name = "STOCK_OUT_COUNT" , nullable = false, length = 500)
    private Integer stockOutCount;

    @Column(name = "STOCK_OUT_TIME" , nullable = false, length = 500)
    private DateTime stockOutTime;

}
