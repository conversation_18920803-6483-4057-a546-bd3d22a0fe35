package com.stpl.tech.kettle.channelpartner.core.cache;

import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.core.queue.OrderNotificationQueue;
import com.stpl.tech.kettle.channelpartner.core.queue.model.OrderNotification;
import com.stpl.tech.kettle.channelpartner.core.service.ChannelPartnerCacheService;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderCacheDetail;
import com.stpl.tech.kettle.channelpartner.domain.model.ProductAlias;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyPartnerSupportRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoNotificationRequest;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.DesiChaiCustomProfiles;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitProductMappingDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitProductPricingDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUpsellingSuperCombosProdIdDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.domain.model.*;
import com.stpl.tech.master.recipe.model.CompositeIngredientData;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.sql.Time;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class ChannelPartnerDataCache {

    private static final Logger LOG = LoggerFactory.getLogger(ChannelPartnerDataCache.class);

    @Autowired
    private ChannelPartnerCacheService channelPartnerCacheService;

    @Autowired
    private EnvironmentProperties environmentProperties;

    @Autowired
    private MasterDataCache masterDataCache;

    private Map<String, PartnerDetail> partnerCache;
    private Map<Integer, PartnerDetail> partnerCacheById;
    private Map<String, PartnerOrderCacheDetail> partnerOrderCache;
    private Customer swiggyCustomer;
    private Customer zomatoCustomer;
    //private Map<Integer, List<UnitChannelPartnerMapping>> unitChannelPartnerMappings; //channelPartnerId to mapping obj map
    /*private Map<Integer, Set<Integer>> unitToPartnerMap;*/
    /*private Map<Integer, MenuSequence> menuSequenceMap;*/
    /*private Map<UnitPartnerBrandKey, List<UnitPartnerMenuMapping>> unitPartnerBrandMenuSequenceMap;*/
    private Map<String, Map<Integer, List<String>>> swiggyProductMap;
    private Map<Integer, Map<Integer, ProductAlias>> partnerProductAliasMap;

    private Map<Integer, Map<Integer, Set<Integer>>> unitProductComboMappings;
    private Map<UnitPartnerBrandKey, Map<Integer, Boolean>> comboStockMapping;
    private Map<Integer, Map<Integer, Set<Integer>>> unitComboProductMappings;
    private Map<UnitPartnerBrandKey, PartnerUpsellingSuperCombosProdIdDetail> partnerUnitUpsellingCombosProductMappings;
    private Map<Integer, PartnerUpsellingSuperCombosProdIdDetail> swiggyUnitProductMappings;
    private boolean swiggyStockVersionStatus;
    private Map<UnitPartnerBrandKey, Map<Integer, Map<String, BigDecimal>>> partnerUnitProductPricing;
    private Map<String, DesiChaiCustomProfiles> desiChaiCustomProfilesMap;

    private Map<String, Set<Integer>> openingTimingOfCafes;
    private Map<String, Set<Integer>> gntOpeningTimingOfCafes;

    private Map<String,Customer> partnerCustomerMap;


    @PostConstruct
    public void createCache() {
        partnerCache = new HashMap<>();
        partnerCacheById = new HashMap<>();
        swiggyCustomer = new Customer();
        zomatoCustomer = new Customer();
        //unitChannelPartnerMappings = new HashMap<>();
        partnerOrderCache = new HashMap<>();
        //unitToPartnerMap = new HashMap<>();
        /*unitPartnerBrandMenuSequenceMap = new HashMap<>();*/
        /*menuSequenceMap = new HashMap<>();*/
        swiggyProductMap = new HashMap<>();
        partnerProductAliasMap = new HashMap<>();
        unitProductComboMappings = new HashMap<>();
        comboStockMapping = new HashMap<>();
        unitComboProductMappings = new HashMap<>();
        partnerUnitUpsellingCombosProductMappings = new HashMap<>();
        swiggyUnitProductMappings = new HashMap<>();
        partnerUnitProductPricing = new HashMap<>();
        desiChaiCustomProfilesMap = new HashMap<>();
        openingTimingOfCafes = new HashMap<>();
        gntOpeningTimingOfCafes = new HashMap<>();
        partnerCustomerMap  = new HashMap<>();
        initCache();
    }

    private void initCache() {
        channelPartnerCacheService.getPartners().forEach(partnerDetail -> {
            partnerCache.put(partnerDetail.getPartnerName(), partnerDetail);
            partnerCacheById.put(partnerDetail.getKettlePartnerId(), partnerDetail);
        });
        //initSwiggyCustomer();
//        addPendingOrdersForNotification(30);
//        setupPartnerOrderCache();
//        loadPartnerProductAliases();
//        loadUnitProductComboMappings();
//        loadUnitUpsellingSuperComboMapping();
//        //loadSwiggyUnitProductMappings();
//        loadSwiggyStockVersionStatus(true);
//        loadPartnerUnitProductPricingMap();
//        loadDesiChaiCustomProfilesMap();
//        getUnitWrtTimings();
    }

    public void clearPartnerCache() {
        partnerCache.clear();
        partnerCacheById.clear();
        swiggyCustomer = new Customer();
        zomatoCustomer = new Customer();
        //unitChannelPartnerMappings.clear();
        partnerOrderCache.clear();
        //unitToPartnerMap.clear();
        /*unitPartnerBrandMenuSequenceMap.clear();*/
        /*menuSequenceMap.clear();*/
        swiggyProductMap.clear();
        partnerProductAliasMap.clear();
        unitProductComboMappings.clear();
        unitComboProductMappings.clear();
        partnerUnitUpsellingCombosProductMappings.clear();
        swiggyUnitProductMappings.clear();
        openingTimingOfCafes.clear();
        gntOpeningTimingOfCafes.clear();
        comboStockMapping.clear();
    }

    public Map<String, PartnerDetail> getPartnerCache() {
        return partnerCache;
    }

    public Map<Integer, PartnerDetail> getPartnerCacheById() {
        return partnerCacheById;
    }

    public Customer getSwiggyCustomer() {
        return swiggyCustomer;
    }

    public void setSwiggyCustomer(Customer swiggyCustomer) {
        this.swiggyCustomer = swiggyCustomer;
    }


    public Customer getPartnerKettleCustomer(String partnerName){
        return partnerCustomerMap.get(partnerName);
    }

    public void setPartnerCustomer(String partnerName , Customer customer){
        partnerCustomerMap.put(partnerName,customer);
    }
    public Customer getZomatoCustomer() {
        return zomatoCustomer;
    }

    public void setZomatoCustomer(Customer zomatoCustomer) {
        this.zomatoCustomer = zomatoCustomer;
    }

    /*public Map<Integer, List<UnitChannelPartnerMapping>> getUnitChannelPartnerMappings() {
        return unitChannelPartnerMappings;
    }*/

    public Map<Integer, ProductAlias> getProductAliasMap(Integer partnerId) {
        return partnerProductAliasMap.get(partnerId);
    }

    public Map<Integer, Set<Integer>> getUnitProductComboMappings(Integer unitId) {
        return unitProductComboMappings.get(unitId);
    }

    public Map<Integer, Set<Integer>> getUnitComboProductMappings(Integer unitId) {
        return unitComboProductMappings.get(unitId);
    }

    /*public Map<Integer, MenuSequence> getMenuSequenceMap() {
        if (menuSequenceMap == null || menuSequenceMap.keySet().isEmpty()) {
            loadMenuSequenceMap();
        }
        return menuSequenceMap;
    }*/

    /*public List<UnitChannelPartnerMapping> getUnitsForChannelPartner(Integer partnerId) {
        if (partnerId != null) {
            List<UnitChannelPartnerMapping> mappings = unitChannelPartnerMappings.get(partnerId);
            if (mappings != null) {
                return mappings;
            } else {
                unitChannelPartnerMappings.clear();
                //loadUnitChannelPartnerMappings();
                if (unitChannelPartnerMappings.get(partnerId) == null) {
                    return new ArrayList<>();
                } else {
                    return unitChannelPartnerMappings.get(partnerId);
                }
            }
        }
        return null;
    }*/

    /*public Set<Integer> getUnitToPartnerMap(Integer unitId) {
        if (unitId != null) {
            Set<Integer> partnerSet = unitToPartnerMap.get(unitId);
            if (partnerSet != null) {
                return partnerSet;
            } else {
                if (unitChannelPartnerMappings == null || unitChannelPartnerMappings.keySet().size() == 0) {
                    unitToPartnerMap.clear();
                    //loadUnitChannelPartnerMappings();
                }
                if (unitToPartnerMap.get(unitId) == null) {
                    return new HashSet<>();
                } else {
                    return unitToPartnerMap.get(unitId);
                }
            }
        }
        return null;
    }*/

    /*public List<UnitPartnerMenuMapping> getMenuMappingsByUnitPartnerBrand(Integer unitId, Integer partnerId, Integer brandId) {
        List<UnitPartnerMenuMapping> mappingList = new ArrayList<>();
        UnitPartnerBrandKey key = new UnitPartnerBrandKey(unitId, partnerId, brandId);
        return unitPartnerBrandMenuSequenceMap.get(key);
    }*/

    /*public void clearChannelPartnersForUnit() {
        unitChannelPartnerMappings.clear();
    }*/

    /*private void clearMenuSequenceMap() {
        menuSequenceMap.clear();
    }*/

    public void initPartnerCache() {
        channelPartnerCacheService.getPartners().forEach(partnerDetail -> {
            partnerCache.put(partnerDetail.getPartnerName(), partnerDetail);
            partnerCacheById.put(partnerDetail.getKettlePartnerId(), partnerDetail);
        });
        //initSwiggyCustomer();
        addPendingOrdersForNotification(30);
        setupPartnerOrderCache();
        loadUnitProductComboMappings();
    }

    public void addPendingOrdersForNotification(int minutes) {
        Date date = ChannelPartnerUtils.getDateBeforeOrAfterInSeconds(ChannelPartnerUtils.getCurrentTimestamp(), -minutes * 60);
        List<PartnerOrderDetail> partnerOrderDetails = channelPartnerCacheService.getNotificationPendingOrders(date);
        if (partnerOrderDetails != null && partnerOrderDetails.size() > 0) {
            partnerOrderDetails.forEach(partnerOrderDetail -> setNotificationQueue(partnerOrderDetail.getPartnerName(), partnerOrderDetail));
        }
    }

    public Map<String, Set<Integer>> getChaayosUnitTimings() {
        return openingTimingOfCafes;
    }

    public Map<String, Set<Integer>> getGntUnitTimings() {
        return gntOpeningTimingOfCafes;
    }


    @Scheduled(cron = "0 30 5 * * *")
    public Map<String, Set<Integer>> getUnitWrtTimings() {
        Collection<Unit> units = masterDataCache.getUnits().values();
        Map<Integer, List<UnitHours>> getUnitHours = new HashMap<>();
        Map<Integer, List<UnitHours>> gntUnitHours = new HashMap<>();
        Map<Integer, String> getUnits = new HashMap<>();
        if (!units.isEmpty()) {
            units.forEach(unit -> {
                getUnitHours.put(unit.getId(), unit.getOperationalHours());
                if (!CollectionUtils.isEmpty(unit.getGntOperationalHours())) {
                    gntUnitHours.put(unit.getId(), unit.getGntOperationalHours());
                }else{
                    gntUnitHours.put(unit.getId(),unit.getOperationalHours());
                }
            });
        }
        if (!getUnitHours.isEmpty()) {
            getUnitHours.keySet().forEach(getUnitId ->
                    getUnitHours.get(getUnitId).forEach(getUnitHour -> {
                                if (getUnitHour.getDayOfTheWeekNumber() == AppUtils.getDayOfWeek(AppUtils.getCurrentDate())) {
                                    if (Objects.nonNull(getUnitHour.getDeliveryOpeningTime())) {
                                        Integer modMinutes = (getUnitHour.getDeliveryOpeningTime().toLocalTime().getMinute()) % 15;
                                        Time times;
                                        if (modMinutes != 0) {
                                            Integer addMinutes = 15 - modMinutes;
                                            LocalTime time = getUnitHour.getDeliveryOpeningTime().toLocalTime().plusMinutes(addMinutes);
                                            times = Time.valueOf(time);
                                        } else {
                                            times = getUnitHour.getDeliveryOpeningTime();
                                        }
                                        String openingTime = convertToString(times);
                                        getUnits.put(getUnitId, openingTime);
                                        if (openingTimingOfCafes.containsKey(openingTime)) {
                                            openingTimingOfCafes.get(openingTime).add(getUnitId);
                                        } else {
                                            Set<Integer> timings = new HashSet<>();
                                            timings.add(getUnitId);
                                            openingTimingOfCafes.put(openingTime, timings);
                                        }
                                        LOG.info("Opening time is {} for unitId:{}", openingTime, getUnitId);
                                    } else {
                                        LOG.error("Opening Time is null for unitId {}", getUnitId);
                                    }
                                }
                            }
                    ));
        }
        if (!gntUnitHours.isEmpty()) {
            gntUnitHours.keySet().forEach(gntUnitId ->
                    gntUnitHours.get(gntUnitId).forEach(gntUnitHour -> {
                                if (gntUnitHour.getDayOfTheWeekNumber() == AppUtils.getDayOfWeek(AppUtils.getCurrentDate())) {
                                    if (Objects.nonNull(gntUnitHour.getDeliveryOpeningTime())) {
                                        Integer modMinutes = (gntUnitHour.getDeliveryOpeningTime().toLocalTime().getMinute()) % 15;
                                        Time times;
                                        if (modMinutes != 0) {
                                            Integer addMinutes = 15 - modMinutes;
                                            LocalTime time = gntUnitHour.getDeliveryOpeningTime().toLocalTime().plusMinutes(addMinutes);
                                            times = Time.valueOf(time);
                                        } else {
                                            times = gntUnitHour.getDeliveryOpeningTime();
                                        }
                                        String openingTime = convertToString(times);
                                        getUnits.put(gntUnitId, openingTime);
                                        if (gntOpeningTimingOfCafes.containsKey(openingTime)) {
                                            gntOpeningTimingOfCafes.get(openingTime).add(gntUnitId);
                                        } else {
                                            Set<Integer> timings = new HashSet<>();
                                            timings.add(gntUnitId);
                                            gntOpeningTimingOfCafes.put(openingTime, timings);
                                        }
                                        LOG.info("GNT Opening time is {} for unitId:{}", openingTime, gntUnitId);
                                    } else {
                                        LOG.error("GNT Opening Time is null for unitId {}", gntUnitId);
                                    }
                                }
                            }
                    ));

        }

        return openingTimingOfCafes;


    }

    private void setupPartnerOrderCache() {
        Date date = ChannelPartnerUtils.getDateBeforeOrAfterInSeconds(ChannelPartnerUtils.getCurrentTimestamp(), -24 * 60 * 60);
        List<PartnerOrderCacheDetail> partnerOrderCacheDetails = channelPartnerCacheService.getPastOrdersByTime(date);
        if (partnerOrderCacheDetails != null && partnerOrderCacheDetails.size() > 0) {
            partnerOrderCacheDetails.forEach(partnerOrderCacheDetail -> partnerOrderCache.put(partnerOrderCacheDetail.getPartnerOrderId(), partnerOrderCacheDetail));
        }
    }

    private void setNotificationQueue(String partnerName, PartnerOrderDetail partnerOrderDetail) {
        OrderNotification orderNotification;
        Integer delayMilliseconds;
        switch (partnerName) {
            case "SWIGGY":
                SwiggyPartnerSupportRequest request = new SwiggyPartnerSupportRequest();
                request.setTimestamp(ChannelPartnerUtils.getFormattedTime(ChannelPartnerUtils.getCurrentTimestamp(), "yyyy-MM-dd HH:mm:ss"));
                request.setTimestampOutlet(request.getTimestamp());
                request.setSwiggyOrderId(Long.valueOf(partnerOrderDetail.getPartnerOrderId()));
                request.setExternalOrderId(partnerOrderDetail.getExternalOrderId());
                delayMilliseconds = environmentProperties.getSwiggyConfirmationDelayInSeconds() * 1000;
                orderNotification = new OrderNotification(request, partnerOrderDetail.getPartnerName(), delayMilliseconds);
                break;
            case "ZOMATO":
                ZomatoNotificationRequest zomatoNotificationRequest = new ZomatoNotificationRequest();
                zomatoNotificationRequest.setOrderId(partnerOrderDetail.getPartnerOrderId());
                zomatoNotificationRequest.setPrepTime(15);
                zomatoNotificationRequest.setDeliveryTime(45);
                zomatoNotificationRequest.setExternalOrderId(partnerOrderDetail.getExternalOrderId());
                delayMilliseconds = environmentProperties.getZomatoConfirmationDelayInSeconds() * 1000;
                orderNotification = new OrderNotification(zomatoNotificationRequest, partnerOrderDetail.getPartnerName(), delayMilliseconds);
                break;
            default:
                orderNotification = null;
        }
        if (orderNotification != null) {
            OrderNotificationQueue.getInstance().add(orderNotification);
        }
    }

    /*@SuppressWarnings("unchecked")
    public void loadUnitChannelPartnerMappings() throws URISyntaxException {
        List<UnitChannelPartnerMapping> mappings = masterDataCache.getUnitChannelPartnerMapping(); *//*channelPartnerCacheService.loadUnitChannelPartnerMappings();*//*
        List<UnitChannelPartnerMapping> transformedMappings = new ArrayList<>();
        Gson gson = new GsonBuilder()
                .registerTypeAdapter(Date.class, (JsonDeserializer<Date>) (json, typeOfT, context) -> new Date(json.getAsJsonPrimitive().getAsLong()))
                .registerTypeAdapter(Date.class, (JsonSerializer<Date>) (date, type, jsonSerializationContext) -> new JsonPrimitive(date.getTime()))
                .create();
        //gson = new GsonBuilder().registerTypeAdapter(Date.class, new DateLongFormatTypeAdapter()).create();
        if (mappings != null && mappings.size() > 0) {
            for (Object o : mappings) {
                String data = gson.toJson(o);
                transformedMappings.add(gson.fromJson(data, UnitChannelPartnerMapping.class));
            }
        }
        transformedMappings.forEach(unitChannelPartnerMapping -> {
            //adding unitchannelpartnermappings
            List<UnitChannelPartnerMapping> mappingList = unitChannelPartnerMappings.get(unitChannelPartnerMapping.getChannelPartner().getId());
            if (mappingList == null) {
                mappingList = new ArrayList<>();
            }
            mappingList.add(unitChannelPartnerMapping);
            unitChannelPartnerMappings.put(unitChannelPartnerMapping.getChannelPartner().getId(), mappingList);
            //adding unitToPartnerMap
            Set<Integer> partnerList = unitToPartnerMap.get(unitChannelPartnerMapping.getUnit().getId());
            if (partnerList == null) {
                partnerList = new HashSet<>();
            }
            partnerList.add(unitChannelPartnerMapping.getChannelPartner().getId());
            unitToPartnerMap.put(unitChannelPartnerMapping.getUnit().getId(), partnerList);
        });
    }*/

    /*@SuppressWarnings("unchecked")
    public void loadMenuSequenceMap() {
        clearMenuSequenceMap();
        List<MenuSequence> menuSequences = channelPartnerCacheService.loadMenuSequences();
        List<MenuSequence> transformedSequence = new ArrayList<>();
        Gson gson = new GsonBuilder()
                .registerTypeAdapter(Date.class, (JsonDeserializer<Date>) (json, typeOfT, context) -> new Date(json.getAsJsonPrimitive().getAsLong()))
                .registerTypeAdapter(Date.class, (JsonSerializer<Date>) (date, type, jsonSerializationContext) -> new JsonPrimitive(date.getTime()))
                .create();
        if (menuSequences != null && menuSequences.size() > 0) {
            for (Object o : menuSequences) {
                String data = gson.toJson(o);
                transformedSequence.add(gson.fromJson(data, MenuSequence.class));
            }
        }
        for (MenuSequence menuSequence : transformedSequence) {
            menuSequenceMap.put(menuSequence.getMenuSequenceId(), menuSequence);
        }
    }*/

    /*@SuppressWarnings("unchecked")
    public void loadUnitPartnerBrandMenuSequenceMap() {
        List<UnitPartnerMenuMapping> mappings = masterDataCache.getUnitPartnerMenuMappings();
        List<UnitPartnerMenuMapping> transformedMappings = new ArrayList<>();
        Gson gson = new GsonBuilder()
                .registerTypeAdapter(Date.class, (JsonDeserializer<Date>) (json, typeOfT, context) -> new Date(json.getAsJsonPrimitive().getAsLong()))
                .registerTypeAdapter(Date.class, (JsonSerializer<Date>) (date, type, jsonSerializationContext) -> new JsonPrimitive(date.getTime()))
                .create();
        if (mappings != null && mappings.size() > 0) {
            for (Object o : mappings) {
                String data = gson.toJson(o);
                transformedMappings.add(gson.fromJson(data, UnitPartnerMenuMapping.class));
            }
        }
        for (UnitPartnerMenuMapping menuMapping : transformedMappings) {
            if (menuMapping.getStatus().equalsIgnoreCase(AppConstants.ACTIVE)) {
                UnitPartnerBrandKey key = new UnitPartnerBrandKey(menuMapping.getUnit().getId(), menuMapping.getChannelPartner().getId(), menuMapping.getBrand().getId());
                List<UnitPartnerMenuMapping> mappingList = unitPartnerBrandMenuSequenceMap.get(key);
                if (mappingList == null || mappingList.isEmpty()) {
                    mappingList = new ArrayList<>();
                }
                mappingList.add(menuMapping);
                unitPartnerBrandMenuSequenceMap.put(key, mappingList);
            }
        }
    }*/

    public Map<String, PartnerOrderCacheDetail> getPartnerOrderCache() {
        return partnerOrderCache;
    }

    public List<String> getSwiggyUnitProductMap(Integer kettlePartnerId, Integer unitId, List<String> productIds) {
        String region = masterDataCache.getUnit(unitId).getRegion();
        if (swiggyProductMap.get(region) == null) {
            loadPartnerUnitProductMap(kettlePartnerId, unitId);
        }
        boolean reloadMap = false;
        for (String productId : productIds) {
            if (!reloadMap && !swiggyProductMap.get(region).containsKey(Integer.valueOf(productId))) {
                reloadMap = true;
            }
        }
        if (reloadMap) {
            clearSwiggyUnitProductMap(unitId);
            loadPartnerUnitProductMap(kettlePartnerId, unitId);
        }
        List<String> partnerProductIds = new ArrayList<>();
        for (String productId : productIds) {
            if (swiggyProductMap.get(region).containsKey(Integer.valueOf(productId))) {
                partnerProductIds.addAll(swiggyProductMap.get(region).get(Integer.valueOf(productId)));
            } else {
                partnerProductIds.add(productId);
            }
        }
        return partnerProductIds;

    }

    public void clearSwiggyUnitProductMap(Integer unitId) {
        String region = masterDataCache.getUnit(unitId).getRegion();
        if (swiggyProductMap.get(region) != null) {
            swiggyProductMap.get(region).clear();
        }
    }

    public void loadPartnerUnitProductMap(Integer kettlePartnerId, Integer unitId) {
        LOG.info("Product id to Partner product id map not found in cache so updating cache");
        String region = masterDataCache.getUnit(unitId).getRegion();
        List<String> productList = channelPartnerCacheService.loadPartnerUnitProductMap(kettlePartnerId, region);
        if (productList != null) {
            Map<Integer, List<String>> partnerProductIdsMap = new HashMap<>();
            for (String productId : productList) {
                String deducedProductId = productId;
                if (productId.contains("_")) {
                    deducedProductId = productId.substring(0, productId.indexOf("_"));
                }
                List<String> actualProductIds = partnerProductIdsMap.get(Integer.valueOf(deducedProductId));
                if (actualProductIds == null) {
                    actualProductIds = new ArrayList<>();
                }
                actualProductIds.add(productId);
                partnerProductIdsMap.put(Integer.valueOf(deducedProductId), actualProductIds);
            }
            swiggyProductMap.put(region, partnerProductIdsMap);
        }
    }

    public void loadPartnerProductAliases() {
        channelPartnerCacheService.getPartners().forEach(partnerDetail -> {
            partnerProductAliasMap.put(partnerDetail.getKettlePartnerId(), channelPartnerCacheService.loadProductAliasMap(partnerDetail.getKettlePartnerId()));
        });
    }

    /*public void clearUnitPartnerBrandMenuSequenceMap() {
        unitPartnerBrandMenuSequenceMap.clear();
    }*/

    public void loadUnitProductComboMappings() {
        masterDataCache.getAllUnits().forEach(this::loadUnitProductComboMappings);
    }

    public void loadUnitProductComboMappings(UnitBasicDetail unitBasicDetail) {
        if (UnitStatus.ACTIVE.equals(unitBasicDetail.getStatus()) && UnitCategory.COD.equals(unitBasicDetail.getCategory())) {
            Map<Integer, Set<Integer>> mappings = unitProductComboMappings.get(unitBasicDetail.getId());
            if (mappings == null) {
                mappings = new HashMap<>();
            }
            Map<Integer, Set<Integer>> comboMappings = unitComboProductMappings.get(unitBasicDetail.getId());
            if (comboMappings == null) {
                comboMappings = new HashMap<>();
            }
            for (Product product : masterDataCache.getUnitProductDetails(unitBasicDetail.getId())) {
                if (product.getTaxCode().equalsIgnoreCase("COMBO") &&
                        product.getSubType() != ChannelPartnerServiceConstants.SUPER_COMBO_SUBCATEGORY_ID &&
                        product.getSubType() != ChannelPartnerServiceConstants.HERO_COMBO_SUBCATEGORY_ID) {
                    Set<Integer> productIds = new HashSet<>();
                    boolean skip = false;
                    for (ProductPrice productPrice : product.getPrices()) {
                        if (productPrice.getRecipe() != null && productPrice.getRecipe().getIngredient().getCompositeProduct() != null) {
                            for (CompositeIngredientData compositeIngredientData : productPrice.getRecipe()
                                    .getIngredient().getCompositeProduct().getDetails()) {
                                int inventoryTrackedItems = 0;
                                for (IngredientProductDetail ingredientProductDetail : compositeIngredientData
                                        .getMenuProducts()) {
                                    if (masterDataCache
                                            .getProduct(ingredientProductDetail.getProduct().getProductId())
                                            .isInventoryTracked()) {
                                        inventoryTrackedItems++;
                                    }
                                    productIds.add(ingredientProductDetail.getProduct().getProductId());
                                }
                                if (!skip && inventoryTrackedItems > 1) {
                                    skip = true;
                                }
                            }
                        } else {
                            skip = true;
                        }
                    }
                    if (!skip) {
                        for (Integer productId : productIds) {
                            Set<Integer> comboIds = mappings.get(productId);
                            if (comboIds == null) {
                                comboIds = new HashSet<>();
                            }
                            comboIds.add(product.getId());
                            mappings.put(productId, comboIds);
                        }
                        Set<Integer> comboItemIds = comboMappings.get(product.getId());
                        if (comboItemIds == null) {
                            comboItemIds = new HashSet<>();
                        }
                        comboItemIds.addAll(productIds);
                        comboMappings.put(product.getId(), comboItemIds);
                    }
                }
            }
            unitProductComboMappings.put(unitBasicDetail.getId(), mappings);
            unitComboProductMappings.put(unitBasicDetail.getId(), comboMappings);
        }
    }

    public Map<Integer,Set<Integer>> getUnitProductComboMappingByUnit(Integer unitId){
        return unitProductComboMappings.get(unitId);
    }

    public void loadUnitUpsellingSuperComboMapping() {
        partnerCacheById.keySet().forEach(this::loadUnitUpsellingSuperComboMapping);
    }

    public void loadUnitUpsellingSuperComboMapping(int partnerId) {
        List<PartnerUnitProductMappingDetail> partnerUnitProductMapping = channelPartnerCacheService.getPartnerUnitProductMappings(partnerId);
        setPartnerUnitUpsellingCombosProductMappingsMap(partnerUnitProductMapping);
    }

    public void loadUnitUpsellingSuperComboMapping(Integer partnerId, Integer unitId, Integer brandId) {
        List<PartnerUnitProductMappingDetail> partnerUnitProductMapping = channelPartnerCacheService.getPartnerUnitProductMappingsByPartnerUnitBrand(partnerId, unitId, brandId);
        setPartnerUnitUpsellingCombosProductMappingsMap(partnerUnitProductMapping);
    }

    private void setPartnerUnitUpsellingCombosProductMappingsMap(List<PartnerUnitProductMappingDetail> partnerUnitProductMapping) {
        if (partnerUnitProductMapping != null && !partnerUnitProductMapping.isEmpty()) {
            for (PartnerUnitProductMappingDetail partnerUnitProdIds : partnerUnitProductMapping) {
                UnitPartnerBrandKey key = new UnitPartnerBrandKey(partnerUnitProdIds.getUnitId(), partnerUnitProdIds.getBrandId(), partnerUnitProdIds.getPartnerId());
                PartnerUpsellingSuperCombosProdIdDetail partnerUpsellingCombo = new PartnerUpsellingSuperCombosProdIdDetail();
                if (partnerUnitProdIds.getProductIds() != null && !partnerUnitProdIds.getProductIds().isEmpty()) {
                    partnerUpsellingCombo.setProductsIds(partnerUnitProdIds.getProductIds());
                }
                partnerUpsellingCombo.setUpsellingProdIds(partnerUnitProdIds.getUpsellingProductIds());
                partnerUpsellingCombo.setSuperCombosProdIds(partnerUnitProdIds.getSuperCombosProductIds());
                if (partnerUnitProdIds.getProductVariantsMap() != null && !partnerUnitProdIds.getProductVariantsMap().isEmpty()) {
                    partnerUpsellingCombo.setProductVariantsMap(partnerUnitProdIds.getProductVariantsMap());
                }
                if(!CollectionUtils.isEmpty(partnerUnitProdIds.getProductRecipeVariantMap())){
                    partnerUpsellingCombo.setProductRecipeVariantsMap(partnerUnitProdIds.getProductRecipeVariantMap());
                }
                if(!CollectionUtils.isEmpty(partnerUnitProdIds.getSplitProductMap())){
                    partnerUpsellingCombo.setSplitProductMap(partnerUnitProdIds.getSplitProductMap());
                }
                partnerUpsellingCombo.setAddOnProductIds(partnerUnitProdIds.getAddOnProductIds());
                partnerUnitUpsellingCombosProductMappings.put(key, partnerUpsellingCombo);
            }
        }
    }

    /*public void loadSwiggyUnitProductMappings() {
        swiggyUnitProductMappings.clear();
        Integer partnerId = getPartnerCache().get("SWIGGY").getKettlePartnerId();
        List<PartnerUnitProductMappingDetail> partnerUnitProductMapping = channelPartnerCacheService.getPartnerUnitProductMappings(partnerId);
        if (partnerUnitProductMapping != null && !partnerUnitProductMapping.isEmpty()) {
            for (PartnerUnitProductMappingDetail partnerUnitProdIds : partnerUnitProductMapping) {
                PartnerUpsellingSuperCombosProdIdDetail partnerProdIds = new PartnerUpsellingSuperCombosProdIdDetail();
                partnerProdIds.setProductsIds(partnerUnitProdIds.getProductIds());
                partnerProdIds.setUpsellingProdIds(partnerUnitProdIds.getUpsellingProductIds());
                partnerProdIds.setSuperCombosProdIds(partnerUnitProdIds.getSuperCombosProductIds());
                swiggyUnitProductMappings.put(partnerUnitProdIds.getUnitId(), partnerProdIds);
            }
        }
    }*/

    public Map<UnitPartnerBrandKey, PartnerUpsellingSuperCombosProdIdDetail> getPartnerUnitUpsellingCombosProductMappings() {
        return partnerUnitUpsellingCombosProductMappings;
    }

    /*public Map<Integer, PartnerUpsellingSuperCombosProdIdDetail> getSwiggyUnitProductMappings() {
        return swiggyUnitProductMappings;
    }*/

    /*public void setSwiggyUnitProductMappings(
        Map<Integer, PartnerUpsellingSuperCombosProdIdDetail> swiggyUnitProductMappings) {
        this.swiggyUnitProductMappings = swiggyUnitProductMappings;
    }*/

    public boolean getSwiggyStockVersionStatus() {
        return swiggyStockVersionStatus;
    }

    public void setSwiggyStockVersionStatus(boolean swiggyStockVersionStatus) {
        this.swiggyStockVersionStatus = swiggyStockVersionStatus;
    }

    private void loadSwiggyStockVersionStatus(boolean check) {
        swiggyStockVersionStatus = check;
    }

    public UnitPartnerBrandMappingData getUnitPartnerBrandMappingData(String outletId, Integer partnerId) {
        RestaurantPartnerKey key = new RestaurantPartnerKey(outletId, partnerId);
        return masterDataCache.getUnitPartnerBrandMappingMetaData2().get(key);
    }

    public void loadPartnerUnitProductPricingMap() {
        List<PartnerUnitProductPricingDetail> pricing = channelPartnerCacheService.getPartnerUnitProductPricing();
        if (pricing != null && !pricing.isEmpty()) {
            pricing.forEach(detail ->
                    partnerUnitProductPricing.put(new UnitPartnerBrandKey(detail.getUnitId(), detail.getBrandId(), detail.getPartnerId()), detail.getPricing())
            );
        }
    }


    public void loadPartnerUnitProductPricingMap(PartnerUnitProductPricingDetail detail) {
        partnerUnitProductPricing.put(new UnitPartnerBrandKey(detail.getUnitId(), detail.getBrandId(), detail.getPartnerId()), detail.getPricing());
    }

    public void loadDesiChaiCustomProfilesMap() {
        List<DesiChaiCustomProfiles> profiles = channelPartnerCacheService.getDesiChaiCustomProfiles();
        if (Objects.nonNull(profiles) && !profiles.isEmpty()) {
            profiles.forEach(detail -> desiChaiCustomProfilesMap.put(detail.getProfileName(), detail));
        }
    }

    public Map<UnitPartnerBrandKey, Map<Integer, Map<String, BigDecimal>>> getPartnerUnitProductPricing() {
        return partnerUnitProductPricing;
    }

    public Map<Integer, Map<String, BigDecimal>> getPartnerUnitProductPricing(UnitPartnerBrandKey key) {
        return partnerUnitProductPricing.get(key);
    }

    public Map<String, DesiChaiCustomProfiles> getDesiChaiCustomProfilesMap() {
        return desiChaiCustomProfilesMap;
    }

    public void updateCacheOfTimings() {
        openingTimingOfCafes.clear();
        gntOpeningTimingOfCafes.clear();
        openingTimingOfCafes = new HashMap<>();
        gntOpeningTimingOfCafes = new HashMap<>();
        getUnitWrtTimings();
    }

    public boolean validatePartnerAndMapping(Integer kettlePartnerId, Integer unitId) {
        if (unitId != null && kettlePartnerId != null && masterDataCache.getUnit(unitId) != null) {
            PartnerDetail partnerDetail = getPartnerCacheById().get(kettlePartnerId);
            boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream()
                    .anyMatch(unitChannelPartnerMapping -> unitChannelPartnerMapping.getChannelPartner()
                            .getId() == kettlePartnerId && unitChannelPartnerMapping.getUnit().getId() == unitId);
            return partnerDetail != null && mappingValid;
        }
        return false;
    }

    public String convertToString(Time time) {
        String oldTime = time.toString();
        String newTime = oldTime.replace(':', '_');
        String outputString = newTime.substring(0, newTime.lastIndexOf("_"));
        return outputString;
    }

    public boolean isMappingValid(Integer partnerId,Integer unitId){
        boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream().anyMatch(unitChannelPartnerMapping ->
                unitChannelPartnerMapping.getChannelPartner().getId() == partnerId && unitChannelPartnerMapping.getUnit().getId() == unitId);
        return mappingValid;
    }

    public Map<Integer, Boolean> getComboStockMapping(UnitPartnerBrandKey key, List<String> productIds) {
        Map<Integer, Boolean> stockStatusMap = new HashMap<>();
        try {
            if (Objects.nonNull(comboStockMapping.get(key))) {
                Map<Integer, Boolean> stockMap = comboStockMapping.get(key);
                productIds.stream()
                        .map(productId -> Integer.valueOf(productId.split("_")[0]))
                        .filter(id -> Objects.nonNull(stockMap.get(id)))
                        .forEach(id -> stockStatusMap.put(id, stockMap.get(id)));
            }
        } catch (Exception e) {
            LOG.info("Error while getting stock status from cache :::::: {}", e.getMessage());
        }
        return stockStatusMap;
    }

    public void addToComboStockMapping(UnitPartnerBrandKey key, Integer productId, Boolean stockStatus) {
        try {
            Map<Integer, Boolean> stockMap = comboStockMapping.computeIfAbsent(key, k -> new HashMap<>());
            stockMap.put(productId, stockStatus);
        } catch (Exception e) {
            LOG.info("Error while adding stock status for product id ::::: {} in cache ",productId, e.getMessage());
        }
    }
}
